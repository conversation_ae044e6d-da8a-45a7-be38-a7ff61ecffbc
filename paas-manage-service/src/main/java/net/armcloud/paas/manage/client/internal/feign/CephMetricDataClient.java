package net.armcloud.paas.manage.client.internal.feign;

import io.swagger.annotations.ApiOperation;
import net.armcloud.paas.manage.client.internal.dto.CephClusterChartDTO;
import net.armcloud.paas.manage.client.internal.dto.CephPressureQueryDTO;
import net.armcloud.paas.manage.client.internal.feign.fallback.CephMetricDataFallbackFactory;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @description paas-center-traffic-info服务ceph集群指标相关接口
 * @date 2025/07/29
 */
@FeignClient(
        name = "paas-center-traffic-info",
        contextId = "paas-center-traffic-info-ceph-metric",
        fallbackFactory = CephMetricDataFallbackFactory.class
)
public interface CephMetricDataClient {

    @PostMapping("/traffic-info/open/traffic/edge/ceph/chart")
    Result<List<CephClusterChartDTO>> getCephPressureChart(@RequestBody CephPressureQueryDTO queryDTO);
}
