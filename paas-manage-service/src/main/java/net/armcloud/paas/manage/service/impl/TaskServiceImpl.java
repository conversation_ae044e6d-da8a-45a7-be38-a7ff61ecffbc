package net.armcloud.paas.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.NetPadTaskExecuteLimitExecutedDTO;
import net.armcloud.paas.manage.client.internal.feign.NetPadExecuteTaskLimitClient;
import net.armcloud.paas.manage.component.thread.SafeRun;
import net.armcloud.paas.manage.config.ThreadPoolRegistry;
import net.armcloud.paas.manage.constant.TaskChannelEnum;
import net.armcloud.paas.manage.constant.TaskSelectTypeConstants;
import net.armcloud.paas.manage.constant.TaskStatusConstants;
import net.armcloud.paas.manage.constant.TaskTypeAndChannelEnum;
import net.armcloud.paas.manage.domain.Page;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.mapper.paas.*;
import net.armcloud.paas.manage.mapper.task.PadBackupTaskInfoMapper;
import net.armcloud.paas.manage.mapper.task.PadRestoreTaskInfoMapper;
import net.armcloud.paas.manage.mapper.task.PadTaskMapper;
import net.armcloud.paas.manage.mapper.task.TaskMapper;
import net.armcloud.paas.manage.model.bo.OperatePadAppBO;
import net.armcloud.paas.manage.model.dto.*;
import net.armcloud.paas.manage.model.qto.ListCustomerBackupQTO;
import net.armcloud.paas.manage.model.vo.*;
import net.armcloud.paas.manage.service.ITaskService;
import net.armcloud.paas.manage.utils.SearchCalibrationUtil;
import net.armcloud.paas.manage.utils.SecurityUtils;
import net.armcloud.paas.manage.utils.connections.ListToMapUtil;
import net.armcloud.paas.manage.utils.redis.RedisCacheUtil;

import net.armcloud.paascenter.common.model.entity.filecenter.UserFile;
import net.armcloud.paascenter.common.model.entity.paas.ArmServer;
import net.armcloud.paascenter.common.model.entity.paas.Customer;
import net.armcloud.paascenter.common.model.entity.paas.CustomerUploadImage;
import net.armcloud.paascenter.common.model.entity.paas.Device;
import net.armcloud.paascenter.common.model.entity.task.PadTask;
import net.armcloud.paascenter.common.model.entity.task.TaskQueue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.armcloud.paas.manage.constant.NumberConsts.ZERO;
import static net.armcloud.paas.manage.constant.RedisTaskQueueConstants.PAD_TASK_DETAIL_PULL_MODE_QUEUE_PREFIX;
import static net.armcloud.paas.manage.constant.RedisTaskQueueConstants.PAD_TASK_PULL_MODE_QUEUE_PREFIX;
import static net.armcloud.paas.manage.constant.TaskTypeConstants.*;

@Service
@Slf4j
public class TaskServiceImpl implements ITaskService {
	@Resource
	private SafeRun safeRun;
	@Resource
	private TaskMapper taskMapper;
	@Resource
	private CustomerMapper customerMapper;
	@Resource
	private UserFileMapper userFileMapper;
	@Resource
	private DictMapper dictMapper;
	@Resource
	private PadBackupTaskInfoMapper padBackupTaskInfoMapper;
	@Resource
	private PadRestoreTaskInfoMapper padRestoreTaskInfoMapper;
	@Resource
	@Qualifier("jacksonRedisTemplate")
	public RedisTemplate<String, Object> jacksonRedisTemplate;
	@Resource
	private PadTaskMapper padTaskMapper;
	@Resource
	private PadMapper padMapper;
	@Resource
	private DeviceMapper deviceMapper;
	@Resource
	private ArmServerMapper armServerMapper;
	@Resource
	private CustomerUploadImageMapper customerUploadImageMapper;

	private final static String TaskStatisticCacheKeyPrefix = "task_statistic:";
    @Autowired
    private NetPadExecuteTaskLimitClient netPadExecuteTaskLimitClient;


	@Override
	public Page<TaskVO> listTasks(TaskDTO taskDTO) {
		PageHelper.startPage(taskDTO.getPage(), taskDTO.getRows());
		if (!SecurityUtils.isAdmin()) {
			taskDTO.setCustomerId(SecurityUtils.getUserId());
		}
		List<TaskVO> taskVOS = taskMapper.listTasks(taskDTO);
		taskVOS.forEach(taskVO -> {
			if (taskVO.getCustomerId() != null) {
				CustomerVO customerVO = customerMapper.selectByPrimaryKey(taskVO.getCustomerId());
				if (customerVO != null) {
					taskVO.setCustomerAccount(customerVO.getCustomerAccount());
					taskVO.setCustomerCode(customerVO.getCustomerCode());
				}
			}
			getDictLabel(taskVO);
		});
		return new Page<>(taskVOS);
	}

	@Override
	public Page<TaskVO> listApps(TaskDTO taskDTO) {
		PageHelper.startPage(taskDTO.getPage(), taskDTO.getRows());
		if (!SecurityUtils.isAdmin()) {
			taskDTO.setCustomerId(SecurityUtils.getUserId());
		}
		List<TaskVO> taskVOS = taskMapper.listApps(taskDTO);
		taskVOS.forEach(taskVO -> {
			if (taskVO.getCustomerId() != null) {
				CustomerVO customerVO = customerMapper.selectByPrimaryKey(taskVO.getCustomerId());
				if (customerVO != null) {
					taskVO.setCustomerAccount(customerVO.getCustomerAccount());
				}
			}
			getDictLabel(taskVO);
			taskVO.setTaskName(taskVO.getTaskTypeName());
			if (taskVO.getCustomerFileId() != null) {
				FileVO fileVO = userFileMapper.selectByApp(taskVO.getCustomerFileId());
				if (fileVO != null) {
					taskVO.setAppId(fileVO.getCustomerAppId());
					taskVO.setAppName(fileVO.getAppName());
					taskVO.setPackageName(fileVO.getPackageName());
					taskVO.setVersion(fileVO.getVersion());
					if (StrUtil.isNotEmpty(fileVO.getAppName())) {
						taskVO.setTaskName(taskVO.getTaskTypeName() + "[" + fileVO.getAppName() + "]");
					}
				}
			} else if (UNINSTALL_APP.getType().equals(taskVO.getTaskType()) && StrUtil.isNotEmpty(taskVO.getTaskContent())) {
				// 应用卸载customerFileId为空 则尝试从taskContent中获取
				PadUninstallAppInfoVO padUninstallAppInfoVO = JSONUtil.toBean(taskVO.getTaskContent(), PadUninstallAppInfoVO.class);
				taskVO.setAppName(padUninstallAppInfoVO.getAppName());
				taskVO.setPackageName(padUninstallAppInfoVO.getPackageName());
				taskVO.setVersion(padUninstallAppInfoVO.getVersionName());
				if (StrUtil.isNotEmpty(padUninstallAppInfoVO.getAppName())) {
					taskVO.setTaskName(taskVO.getTaskTypeName() + "[" + padUninstallAppInfoVO.getAppName() + "]");
				}
			}

			// fill operate app information
			parseOperateAppInfo(taskVO);
		});
		return new Page<>(taskVOS);
	}

	private void parseOperateAppInfo(TaskVO taskVO) {
		List<Integer> appTaskTypes = Arrays.asList(STOP_APP.getType(), START_APP.getType(), RESTART_APP.getType());
		if (!appTaskTypes.contains(taskVO.getTaskType())) {
			return;
		}

		String taskContent = taskVO.getTaskContent();
		if (StringUtils.isBlank(taskContent)) {
			return;
		}

		OperatePadAppBO operatePadAppBO = JSON.parseObject(taskContent, OperatePadAppBO.class);
		if (operatePadAppBO == null) {
			return;
		}

		taskVO.setTaskName(taskVO.getTaskTypeName());
		taskVO.setAppName(operatePadAppBO.getAppName());
		taskVO.setPackageName(operatePadAppBO.getPkgName());
		taskVO.setVersion(operatePadAppBO.getVersionName());
	}

	private void parseOperateAppInfo(AllTaskVO taskVO) {
		List<Integer> appTaskTypes = Arrays.asList(STOP_APP.getType(), START_APP.getType(), RESTART_APP.getType());
		if (!appTaskTypes.contains(taskVO.getTaskType())) {
			return;
		}

		String taskContent = taskVO.getTaskContent();
		if (StringUtils.isBlank(taskContent)) {
			return;
		}

		OperatePadAppBO operatePadAppBO = JSON.parseObject(taskContent, OperatePadAppBO.class);
		if (operatePadAppBO == null) {
			return;
		}

		taskVO.setTaskName(taskVO.getTaskTypeName());
		taskVO.setAppName(operatePadAppBO.getAppName());
		taskVO.setPackageName(operatePadAppBO.getPkgName());
		taskVO.setVersion(operatePadAppBO.getVersionName());
	}

	@Override
	public Page<TaskVO> uploadListTasks2(TaskDTO taskDTO) {
		PageHelper.startPage(taskDTO.getPage(), taskDTO.getRows(),false);
		if (!SecurityUtils.isAdmin()) {
			taskDTO.setCustomerId(SecurityUtils.getUserId());
		}
		List<TaskVO> taskVOS = taskMapper.uploadListTasks(taskDTO);
		if (CollUtil.isNotEmpty(taskVOS)) {
			// 翻译dict数据字典
			setTaskDictLabel(taskVOS);

			List<Long> customerFileIds = taskVOS.stream().filter(item -> item.getCustomerFileId() != null)
					.map(item -> item.getCustomerFileId()).collect(Collectors.toList());
			if (CollUtil.isNotEmpty(customerFileIds)) {
				LambdaQueryWrapper<UserFile> queryWrapper = new LambdaQueryWrapper();
				queryWrapper.in(UserFile::getId, customerFileIds);
				List<UserFile> userFiles = userFileMapper.selectList(queryWrapper);
				if (CollUtil.isNotEmpty(userFiles)) {
					taskVOS.forEach(taskVO -> {
						userFiles.forEach(userFile -> {
							if (userFile.getId().equals(taskVO.getCustomerFileId())) {
								taskVO.setFileId(userFile.getFileUniqueId());
								taskVO.setFileName(userFile.getFileName());
								if (StrUtil.isNotEmpty(userFile.getFileName())) {
									taskVO.setTaskName("文件上传[" + userFile.getFileName() + "]");
								}
							}
						});
					});
				}
			}
		}

		return new Page<>(taskVOS);

	}

	@Override
	public Page<TaskVO> uploadListTasks(TaskDTO taskDTO) {
		PageHelper.startPage(taskDTO.getPage(), taskDTO.getRows());
		if (!SecurityUtils.isAdmin()) {
			taskDTO.setCustomerId(SecurityUtils.getUserId());
		}
		List<TaskVO> taskVOS = taskMapper.uploadListTasks(taskDTO);
		taskVOS.forEach(taskVO -> {
			if (taskVO.getCustomerId() != null) {
				CustomerVO customerVO = customerMapper.selectByPrimaryKey(taskVO.getCustomerId());
				if (customerVO != null) {
					taskVO.setCustomerAccount(customerVO.getCustomerAccount());
				}
			}
			if (taskVO.getCustomerFileId() != null) {
				FileVO fileVO = userFileMapper.selectByFile(taskVO.getCustomerFileId());
				if (fileVO != null) {
					taskVO.setFileId(fileVO.getUniqueId());
					taskVO.setFileName(fileVO.getFileName());
					if (StrUtil.isNotEmpty(fileVO.getFileName())) {
						taskVO.setTaskName("文件上传[" + fileVO.getFileName() + "]");
					}
				}
			}
			getDictLabel(taskVO);
		});
		return new Page<>(taskVOS);
	}

	public TaskDTO taskCustomerSearch(TaskDTO param) {
		String taskQuery = param.getTaskQuery();
		String fileId = param.getFileId();
		String fileName = param.getFileName();

		// 根据任务查询类型获取所有对应的任务编码
		if (param.getTaskSelectType() != null) {
			param.setAllTaskTypes(TaskTypeAndChannelEnum.taskCodesFromTaskSelectType(param.getTaskSelectType()));
		}
		// 任务查询判断
		if (StrUtil.isNotEmpty(taskQuery)) {
			SearchCalibrationUtil.taskSearch(param);
		}
		// 应用查询判断
		// 根据appid查询到的文件id
		if (StrUtil.isNotBlank(param.getAppId())) {
			// 查询到appid对应的文件id
			List<String> fileCustomers = userFileMapper.selectByAppId(param.getAppId(), null, null);
			if (CollUtil.isNotEmpty(fileCustomers)) {
				param.setAppIds(fileCustomers);
			} else {
				return null;
			}
		}
		if (StrUtil.isNotBlank(param.getAppName())) {
			List<String> fileIdList = userFileMapper.selectByAppId(null, param.getAppName(), null);
			if (CollUtil.isNotEmpty(fileIdList)) {
				param.setAppIds(fileIdList);
			} else {
				return null;
			}
		}
		if (StrUtil.isNotEmpty(param.getPackageName())) {
			List<String> fileIdList = userFileMapper.selectByAppId(null, null, param.getPackageName());
			if (CollUtil.isNotEmpty(fileIdList)) {
				param.setAppIds(fileIdList);
			} else {
				return null;
			}
		}
		// 客户查询
		if (StrUtil.isNotBlank(param.getCustomerAccount()) || param.getCustomerId() != null) {
			CustomerDTO customerDTO = new CustomerDTO();
			customerDTO.setCustomerAccount(param.getCustomerAccount());
			customerDTO.setCustomerCode(param.getCustomerCode());
			List<Long> customerIdList = customerMapper.selectByIdAndAccount(customerDTO);
			if (CollUtil.isNotEmpty(customerIdList)) {
				param.setCustomerIds(customerIdList);
			} else {
				return null;
			}
		}

		if (StrUtil.isNotBlank(fileName) || StrUtil.isNotEmpty(fileId)) {
			FileDTO dto = new FileDTO();
			dto.setFileName(fileName);
			dto.setFileId(fileId);
			List<String> fileIdList = userFileMapper.selectByCustomerFileId(dto);
			if (fileIdList.isEmpty()) {
				return null;
			} else {
				param.setFileIds(fileIdList);
			}
		}

		// 查询板卡下的实例编号
		if (param.getIdNoType() != null && param.getIdNoType() == 1 && StrUtil.isNotEmpty(param.getIdNo())) {
			List<String> padCodeList = padMapper.getPadCodesByDeviceCode(param.getIdNo());
			param.setPadCodes(padCodeList);
		}
		return param;
	}

	@Override
	public Page<TaskVO> listDeviceTasks(TaskDTO taskDTO) {
		PageHelper.startPage(taskDTO.getPage(), taskDTO.getRows());
		if (!SecurityUtils.isAdmin()) {
			taskDTO.setCustomerId(SecurityUtils.getUserId());
		}
		List<TaskVO> taskVOS = taskMapper.listDeviceTasks(taskDTO);
		taskVOS.forEach(taskVO -> {
			if (taskVO.getCustomerId() != null) {
				CustomerVO customerVO = customerMapper.selectByPrimaryKey(taskVO.getCustomerId());
				if (customerVO != null) {
					taskVO.setCustomerAccount(customerVO.getCustomerAccount());
					taskVO.setCustomerCode(customerVO.getCustomerCode());
				}
			}
			getDictLabel(taskVO);
		});
		return new Page<>(taskVOS);
	}

	@Override
	public Page<TaskBackupVO> listBackupTasks(TaskBackupDTO taskBackupDTO) {
		PageHelper.startPage(taskBackupDTO.getPage(), taskBackupDTO.getRows());
		if (!SecurityUtils.isAdmin()) {
			taskBackupDTO.setCustomerId(SecurityUtils.getUserId());
		}
		List<TaskBackupVO> taskBackupVOS = padBackupTaskInfoMapper.listBackupTasks(taskBackupDTO);
		if (CollUtil.isNotEmpty(taskBackupVOS)) {
			Map<Long, String> latestBackups = getLatestBackupByPadcodeAsMap(taskBackupVOS);
			List<Long> ids = taskBackupVOS.stream().map(TaskBackupVO::getCustomerId).collect(Collectors.toList());
			List<Customer> customers = customerMapper.selectByIds(ids);
			taskBackupVOS.forEach(taskBackupVO -> {
				if (latestBackups.containsKey(taskBackupVO.getId())) {
					taskBackupVO.setLatest(true);
				}
				for (Customer customer : customers) {
					if (taskBackupVO.getCustomerId().equals(customer.getId())) {
						taskBackupVO.setCustomerAccount(customer.getCustomerAccount());
						break;
					}
				}
				getBackupDictLabel(taskBackupVO);
			});
		}
		return new Page<>(taskBackupVOS);
	}

	@Override
	public Page<TaskRestoreVO> listRestoreTasks(TaskRestoreDTO taskRestoreDTO) {
		PageHelper.startPage(taskRestoreDTO.getPage(), taskRestoreDTO.getRows());
		if (!SecurityUtils.isAdmin()) {
			taskRestoreDTO.setCustomerId(SecurityUtils.getUserId());
		} else {
			// 管理员账号查询所有数据
			taskRestoreDTO.setCustomerId(null);
		}

		List<TaskRestoreVO> taskRestoreVOS = padRestoreTaskInfoMapper.listRestoreTasks(taskRestoreDTO);
		if (CollUtil.isNotEmpty(taskRestoreVOS)) {
			Map<Long, String> latestRestores = getLatestRestoreByPadcodeAsMap(taskRestoreVOS);
			List<Long> ids = taskRestoreVOS.stream().map(TaskRestoreVO::getCustomerId).collect(Collectors.toList());
			List<Customer> customers = customerMapper.selectByIds(ids);
			taskRestoreVOS.forEach(taskRestoreVO -> {
				taskRestoreVO.setBackupSizeDesc(calculateBackupSize(taskRestoreVO.getBackupSize()));
				if (latestRestores.containsKey(taskRestoreVO.getId())) {
					taskRestoreVO.setLatest(true);
				}
				for (Customer customer : customers) {
					if (taskRestoreVO.getCustomerId().equals(customer.getId())) {
						taskRestoreVO.setCustomerAccount(customer.getCustomerAccount());
						break;
					}
				}
				getRestoreDictLabel(taskRestoreVO);
			});
		}
		return new Page<>(taskRestoreVOS);
	}

	/**
	 * 计算备份文件大小
	 *
	 * @param backupSize 备份文件原始字节大小
	 * @return 单位MB
	 */
	private String calculateBackupSize(Long backupSize) {
		if (backupSize == null) {
			return "0MB";
		}

		return Math.max(0, backupSize / 1024 / 1024) + "MB";
	}

	private static LocalDateTime parseCreateTime(String createTime) {
		DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		return LocalDateTime.parse(createTime, FORMATTER);
	}

	public static Map<Long, String> getLatestRestoreByPadcodeAsMap(List<TaskRestoreVO> taskRestoreVOList) {
		return taskRestoreVOList.stream().filter(task -> Objects.equals(task.getStatus(), TaskStatusConstants.FAIL_ALL.getStatus())).collect(Collectors.groupingBy(TaskRestoreVO::getPadCode, Collectors.maxBy(Comparator.comparing(o -> parseCreateTime(o.getCreateTime()))))).values().stream().map(optional -> optional.orElse(null)).filter(Objects::nonNull).collect(Collectors.toMap(TaskRestoreVO::getId, TaskRestoreVO::getPadCode));
	}

	public static Map<Long, String> getLatestBackupByPadcodeAsMap(List<TaskBackupVO> taskBackupVOList) {
		return taskBackupVOList.stream().filter(task -> Objects.equals(task.getStatus(), TaskStatusConstants.FAIL_ALL.getStatus())).collect(Collectors.groupingBy(TaskBackupVO::getPadCode, Collectors.maxBy(Comparator.comparing(o -> parseCreateTime(o.getCreateTime()))))).values().stream().map(optional -> optional.orElse(null)).filter(Objects::nonNull).collect(Collectors.toMap(TaskBackupVO::getId, TaskBackupVO::getPadCode));
	}

	@Override
	public List<CustomerBackupVO> listCustomerBackups(ListCustomerBackupDTO dto) {
		ListCustomerBackupQTO qto = new ListCustomerBackupQTO();
		qto.setCustomerId(SecurityUtils.getUserId());
		qto.setBackupName(dto.getBackupName());

		if (SecurityUtils.isAdmin()) {
			qto.setCustomerId(null);
		}

		return padBackupTaskInfoMapper.listCustomerBackups(qto);
	}

	@Override
	public void deleteBackups(List<Long> backupIds) {
		padBackupTaskInfoMapper.deleteBackupByIds(backupIds);
	}

	/**
	 * 取消实例任务
	 *
	 * @param param
	 * @return
	 */
	@Override
	public Boolean cancelPadTask(CancelPadTaskDTO param) {
		// 这里要查待执行的
		PadTask padTask = padTaskMapper.selectPadCodeById(param.getTaskId().longValue());
		int count = taskMapper.cancelPadTask(param);
		if (count > ZERO) {
			// 清除拉模式下缓存队列
			if (padTask != null) {
				// 查询一下实例编号和实例ip及集群编号
				PadAndDeviceInfoVO padAndDeviceInfoVO = padMapper.selectPadAndDeviceInfo(padTask.getPadCode());
				if (padAndDeviceInfoVO != null) {
					TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(padTask.getType());
					if (taskTypeAndChannelEnum != null) {
						removePadTaskPullMode(taskTypeAndChannelEnum, padAndDeviceInfoVO.getClusterCode(), padAndDeviceInfoVO.getDeviceIp(), padAndDeviceInfoVO.getPadCode(), padTask.getId());
						// 调用paas-center-core服务推送任务状态变更。
						NetPadTaskExecuteLimitExecutedDTO dto = new NetPadTaskExecuteLimitExecutedDTO();
						dto.setTaskType(String.valueOf(padTask.getType()));
						dto.setClusterCode(padAndDeviceInfoVO.getClusterCode());
						dto.setCustomerId(padTask.getCustomerId());
						dto.setPadCode(padTask.getPadCode());
						netPadExecuteTaskLimitClient.executed(dto);
					}
				}
			}

			// todo 这里使用的是customer_task_id 这个id不同用户下可能重复的 只是现在操作频率不高 碰不到而已
			taskMapper.cancelRefreshMasterTaskStatus(param.getTaskId());
			return true;
		}
		return false;
	}

	/**
	 * 删除任务
	 * cbs任务为板卡ip bmc为arm服务器ip gameserver任务为实例编号
	 *
	 * @param taskTypeAndChannelEnum
	 * @param clusterCode            集群编号
	 * @param deviceIp               设备ip
	 * @param deviceCode             设备编号
	 * @param padTaskId              子任务id
	 */
	public void removePadTaskPullMode(TaskTypeAndChannelEnum taskTypeAndChannelEnum, String clusterCode, String deviceIp, String deviceCode, long padTaskId) {
		String cacheKey = PAD_TASK_PULL_MODE_QUEUE_PREFIX + taskTypeAndChannelEnum.getChannel() + ":";
		if (TaskChannelEnum.GAMESERVER.getCode().equals(taskTypeAndChannelEnum.getChannel())) {
			cacheKey += deviceCode;
		} else {
			cacheKey += clusterCode + ":" + deviceIp;
		}
		String padTaskIdStr = null;
		if ("device".equals(taskTypeAndChannelEnum.getSmallerType())) {
			padTaskIdStr = padTaskId + "_device";
		} else if ("device".equals(taskTypeAndChannelEnum.getSmallerType())) {
			padTaskIdStr = padTaskId + "_arm";
		} else {
			padTaskIdStr = String.valueOf(padTaskId);
		}
		jacksonRedisTemplate.opsForZSet().remove(cacheKey, padTaskIdStr);
		String deviceCacheDetailKey = PAD_TASK_DETAIL_PULL_MODE_QUEUE_PREFIX + padTaskIdStr;
		jacksonRedisTemplate.expire(deviceCacheDetailKey, 1, TimeUnit.SECONDS);
	}

	/**
	 * 根据任务类型和任务状态查询任务
	 *
	 * @param pads
	 * @param status
	 * @return
	 */
	@Override
	public int selectTaskByTaskTypeAndTaskStatus(List<String> pads, List<Integer> status) {
		return taskMapper.selectTaskByTaskTypeAndTaskStatus(pads, status);
	}

	@Override
	public boolean cancelDeviceTask(CancelPadTaskDTO param) {
		PullModeDeviceTaskVO pullModeDeviceTaskVO = taskMapper.selectDeviceCodeById(param.getTaskId().longValue());
		int count = taskMapper.cancelDeviceTask(param);
		if (count > ZERO) {
			// 清除拉模式下缓存队列
			if (pullModeDeviceTaskVO != null) {
				TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(pullModeDeviceTaskVO.getType());
				if (taskTypeAndChannelEnum != null) {
					if (TaskChannelEnum.BMC.getCode().equals(taskTypeAndChannelEnum.getChannel())) {
						ArmServer armServer = armServerMapper.selectByArmServerCode(pullModeDeviceTaskVO.getDeviceCode());
						if (armServer != null) {
							removePadTaskPullMode(taskTypeAndChannelEnum, armServer.getClusterCode(), armServer.getArmIp(), armServer.getArmServerCode(), pullModeDeviceTaskVO.getId());
						}
					} else {
						// 查询一下实例编号和实例ip及集群编号
						List<Device> deviceList = deviceMapper.selectByDeviceCode(Arrays.asList(pullModeDeviceTaskVO.getDeviceCode()));
						if (CollUtil.isNotEmpty(deviceList)) {
							Device device = deviceList.get(0);
							String clusterCode = armServerMapper.getClusterCodeByArmServerCode(device.getArmServerCode());
							removePadTaskPullMode(taskTypeAndChannelEnum, clusterCode, device.getDeviceIp(), device.getDeviceCode(), pullModeDeviceTaskVO.getId());
						}
					}
				}
			}

			taskMapper.cancelDeviceRefreshMasterTaskStatus(param.getTaskId());
			return true;
		}
		return false;
	}

	/**
	 * 任务统计
	 *
	 * @return
	 */
	@Override
	public Result<TaskStatisticVO> taskStatistic() {
		Long customerId;
		if (!SecurityUtils.isAdmin()) {
			customerId = SecurityUtils.getUserId();
		} else {
			customerId = null;
		}
		String cacheKey = TaskStatisticCacheKeyPrefix + (customerId == null ? "admin" : customerId);
		try {
			TaskStatisticVO taskStatisticVO = RedisCacheUtil.cacheData(
					cacheKey, 5,
					() -> this.generateStatistic(customerId),
					true);
			return Result.ok(taskStatisticVO);
		} catch (Exception e) {
			log.error("taskStatistic error", e);
			return Result.ok();
		}
	}

	public TaskStatisticVO generateStatistic(Long customerId) {

		log.info("generateStatistic start customerId:{}", customerId);
		TaskStatisticVO data = new TaskStatisticVO();
		TaskStatisticVO fileTaskStatistic = taskMapper.getFileTaskStatistic(customerId);
		TaskStatisticVO deviceTaskStatistic = taskMapper.getDeviceTaskStatistic(customerId);
		TaskStatisticVO taskStatisticVO = padTaskStatistic(customerId);
		// 累加统计值
		data.setSuccessNum((taskStatisticVO != null ? taskStatisticVO.getSuccessNum() : 0) + (fileTaskStatistic != null ? fileTaskStatistic.getSuccessNum() : 0) + (deviceTaskStatistic != null ? deviceTaskStatistic.getSuccessNum() : 0));

		data.setFailNum((taskStatisticVO != null ? taskStatisticVO.getFailNum() : 0) + (fileTaskStatistic != null ? fileTaskStatistic.getFailNum() : 0) + (deviceTaskStatistic != null ? deviceTaskStatistic.getFailNum() : 0));

		data.setExecutingNum((taskStatisticVO != null ? taskStatisticVO.getExecutingNum() : 0) + (fileTaskStatistic != null ? fileTaskStatistic.getExecutingNum() : 0) + (deviceTaskStatistic != null ? deviceTaskStatistic.getExecutingNum() : 0));
		data.setWaitingNum((taskStatisticVO != null ? taskStatisticVO.getWaitingNum() : 0) + (fileTaskStatistic != null ? fileTaskStatistic.getWaitingNum() : 0) + (deviceTaskStatistic != null ? deviceTaskStatistic.getWaitingNum() : 0));
		data.setFailPadNum(taskStatisticVO != null ? taskStatisticVO.getFailPadNum() : 0);
		log.info("generateStatistic end customerId:{},data:{}", customerId, data);
		return data;
	}

	public TaskStatisticVO padTaskStatistic(Long customerId) {
		log.info("padTaskStatistic start customerId:{}", customerId);

		try {
			// 1. 获取汇总数据（使用缓存和分布式锁）
			TaskStatisticCacheData summaryData = getSummaryDataWithCache(customerId);

			// 2. 获取失败实例数（使用Redis Set缓存）
			int failPadNum = getFailPadNumWithCache(customerId);

			// 3. 组装返回结果
			TaskStatisticVO result = summaryData.toTaskStatisticVO();
			result.setFailPadNum(failPadNum);

			log.info("padTaskStatistic end customerId:{}, result:{}", customerId, result);
			return result;
		} catch (Exception e) {
			log.error("padTaskStatistic error customerId:{}", customerId, e);
			// 异常时返回空数据
			return null;
		}
	}

	/**
	 * 获取汇总数据（使用缓存和分布式锁）
	 */
	private TaskStatisticCacheData getSummaryDataWithCache(Long customerId) {

		// 获取当前小时
		LocalDateTime now = LocalDateTime.now();
		int currentHour = now.getHour();
		return generateSummaryData(customerId, currentHour, false);
	}

	/**
	 * 生成汇总数据（递归获取历史缓存数据）
	 */
	private TaskStatisticCacheData generateSummaryData(Long customerId, Integer currentHour, boolean isHistoricalData) {
		log.info("generateSummaryData start customerId:{}, currentHour:{}", customerId, currentHour);

		TaskStatisticCacheData result = new TaskStatisticCacheData();
		result.setCacheTimestamp(System.currentTimeMillis());

		// 如果是0点，直接查询当前小时的数据
		if (currentHour <= 1) {
			return queryIncrementalData(customerId, result, 0L, currentHour, isHistoricalData);
		}

		// 递归获取上一个整点的缓存数据
		int previousHour = currentHour - 1;
		String previousCacheKey = buildSummaryCacheKey(customerId, previousHour);

		// 检查上一个整点的缓存是否存在
		TaskStatisticCacheData previousData = RedisCacheUtil.getCache(previousCacheKey, TaskStatisticCacheData.class);
		if (previousData == null) {
			// 递归获取上一个整点的数据
			previousData = generateSummaryData(customerId, previousHour, true);
			// 直接缓存上一个整点的数据，12小时过期
			RedisCacheUtil.setCacheData(previousCacheKey, previousData, 12 * 60 * 60);
		}

		// 合并上一个整点的数据
		result.merge(previousData);

		// 查询当前时间段的增量数据
		return queryIncrementalData(customerId, result, previousData.getLastId(), currentHour, isHistoricalData);
	}

	/**
	 * 查询增量数据
	 * @param customerId 客户ID
	 * @param result 结果对象
	 * @param fromId 起始ID
	 * @param currentHour 当前查询的小时
	 * @param isHistoricalData 是否为历史数据查询
	 */
	private TaskStatisticCacheData queryIncrementalData(Long customerId, TaskStatisticCacheData result, Long fromId, int currentHour, boolean isHistoricalData) {
		LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
		if (fromId == null || fromId == 0) {
			// 如果没有fromId，获取当前传入的小时时间段内的第一条数据的id
			String endTime = today.withHour(currentHour + 1)
					.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			fromId = taskMapper.getFirstIdFromToday(endTime, customerId);
			// 缓存当前小时的第一条数据的id
			if (fromId == null) {
				// 当前小时无数据直接返回。
				return result;
			}
			// 初次查询到fromId数据时，需要做－1处理，不然无法统计该条数据。
			fromId--;
		}

		Long endId;
		if (!isHistoricalData) {
			// 查询当前小时的增量数据，使用最新的maxId
			endId = taskMapper.getMaxId();
		} else {
			// 查询历史数据，使用传入小时的开始时间作为最终时间，留出1小时防止数据变化
			String beforeTime = today.withHour(currentHour)
					.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			endId = taskMapper.getMaxIdBeforeTime(beforeTime, customerId);
		}

		if (endId == null || endId <= fromId) {
			endId = fromId;
		}

		// 分批查询增量数据
		int step = 100000;
		List<CompletableFuture<TaskStatisticVO>> futures = new ArrayList<>();

		for (long from = fromId + 1; from <= endId; from += step) {
			long to = Math.min(from + step - 1, endId);
			long finalFrom = from;

            CompletableFuture<TaskStatisticVO> future = CompletableFuture.supplyAsync(
					() -> taskMapper.getPadTaskStatisticByIdRangeWithoutFailPad(finalFrom, to, customerId),
					ThreadPoolRegistry.STATISTIC_POOL);
			futures.add(future);
		}

		// 合并结果
		for (CompletableFuture<TaskStatisticVO> f : futures) {
			TaskStatisticVO part = f.join();
			result.merge(part);
		}

		result.setLastId(endId);
		return result;
	}

	/**
	 * 构建汇总数据缓存key(5s缓存时间的实时数据缓存）
	 */
	private String buildSummaryCacheKey(Long customerId) {
		String customerKey = customerId == null ? "admin" : customerId.toString();
		String dateKey = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
		return String.format("task:summary:%s:%s", customerKey, dateKey);
	}

	/**
	 * 构建汇总数据缓存key（每小时的备份数据缓存）
	 */
	private String buildSummaryCacheKey(Long customerId, int hour) {
		String customerKey = customerId == null ? "admin" : customerId.toString();
		String dateKey = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
		return String.format("task:summary:%s:%s:%02d", customerKey, dateKey, hour);
	}

	/**
	 * 获取失败实例数（使用Redis Set缓存）
	 */
	private int getFailPadNumWithCache(Long customerId) {
		try {
			// 更新失败实例缓存
			updateFailPadCache(customerId);

			// 统计所有分区的失败实例数
			int totalFailPadNum = 0;
			for (int i = 0; i < 10; i++) {
				String setKey = buildFailPadSetKey(customerId, i);
				Long setSize = jacksonRedisTemplate.opsForSet().size(setKey);
				totalFailPadNum += setSize != null ? setSize.intValue() : 0;
			}

			return totalFailPadNum;
		} catch (Exception e) {
			log.error("getFailPadNumWithCache error customerId:{}", customerId, e);
			return 0;
		}
	}

	/**
	 * 更新失败实例缓存
	 */
	private void updateFailPadCache(Long customerId) {
		String cacheKey = buildFailPadCacheKey(customerId);

		// 获取上次缓存的ID信息
		FailPadCacheData cacheData = RedisCacheUtil.getCache(cacheKey, FailPadCacheData.class);
		if (cacheData == null) {
			cacheData = new FailPadCacheData();
		}

		Long fromId = cacheData.getLastFailId();
		if (fromId == null) {
			fromId = 0L;
		}

		// 查询新的失败实例
		List<String> newFailPadCodes = taskMapper.getFailPadCodesFromId(fromId, customerId);

		// 按分片总数分组存储失败实例
		if (CollUtil.isNotEmpty(newFailPadCodes)) {
			int shardCount = 10;
			Map<Integer, List<String>> shardMap = newFailPadCodes.stream()
					.collect(Collectors.groupingBy(padCode -> Math.abs(padCode.hashCode()) % shardCount));
			for (int i = 0; i < shardCount; i++) {
				List<String> padCodes = shardMap.get(i);
				if (CollUtil.isNotEmpty(padCodes)) {
					String setKey = buildFailPadSetKey(customerId, i);
					jacksonRedisTemplate.opsForSet().add(setKey, padCodes.toArray());
					jacksonRedisTemplate.expire(setKey, 24 * 60 * 60, TimeUnit.SECONDS);
				}
			}
		}

		// 统一使用时间最靠前的执行中记录ID作为lastFailId，防止数据变化
		Long lastExecutingId = taskMapper.getLastExecutingTaskId(fromId, customerId);
		if (lastExecutingId != null && lastExecutingId > 0) {
			cacheData.setLastFailId(lastExecutingId - 1);
		} else {
			// 如果没有执行中的记录，保持原有的fromId
			cacheData.setLastFailId(fromId);
		}

		// 更新缓存
		cacheData.setCacheTimestamp(System.currentTimeMillis());
		RedisCacheUtil.setCacheData(cacheKey, cacheData, 12 * 60 * 60); // 3小时过期
	}

	/**
	 * 构建失败实例缓存key
	 */
	private String buildFailPadCacheKey(Long customerId) {
		String customerKey = customerId == null ? "admin" : customerId.toString();
		String dateKey = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
		return String.format("task:summary:failpad:cache:%s:%s", customerKey, dateKey);
	}

	/**
	 * 构建失败实例Set缓存key
	 */
	private String buildFailPadSetKey(Long customerId, int partition) {
		String customerKey = customerId == null ? "admin" : customerId.toString();
		String dateKey = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
		return String.format("task:summary:failpad:set:%s:%s:%d", customerKey, dateKey, partition);
	}

	public boolean isRunningBackup(List<String> padCodes) {
		int num = padBackupTaskInfoMapper.countPadBackupTasks(padCodes, TaskStatusConstants.EXECUTING.getStatus());
		return num > ZERO;
	}

	public boolean isRunningRestore(List<String> padCodes) {
		int num = padRestoreTaskInfoMapper.countPadRestoreTasks(padCodes, TaskStatusConstants.EXECUTING.getStatus());
		return num > ZERO;
	}


	@Override
	public List<SuccessTaskStaticVo> successTaskStatic(TaskStaticDTO dto) {
		if (Objects.nonNull(dto)) {
			if (StringUtils.isNotEmpty(dto.getStartTime())) {
				dto.setStartTime(dto.getStartTime() + " 00:00:00");
			}
			if (StringUtils.isNotEmpty(dto.getEndTime())) {
				dto.setEndTime(dto.getEndTime() + " 23:59:59");
			}
		}
		List<DictVO> dictVOS = dictMapper.getAll();
		Map<String, String> dictMap = dictVOS.stream().filter(dictVO -> dictVO.getDictValue() != null && !dictVO.getDictValue().isEmpty() && dictVO.getDictLabel() != null && !dictVO.getDictLabel().isEmpty()) // 过滤掉任何字段为空的
				.collect(Collectors.toMap(DictVO::getDictValue, DictVO::getDictLabel, (v1, v2) -> v1)); // 保留第一个值

		List<SuccessTaskStaticVo> list = taskMapper.successTaskStatic(dto);
		list.forEach(bean -> bean.setTypeName(dictMap.getOrDefault(bean.getType(), "未知")));
		return list;
	}

	@Override
	public List<SuccessTaskStaticVo> failTaskStatic(TaskStaticDTO dto) {
		if (StringUtils.isNotEmpty(dto.getStartTime())) {
			dto.setStartTime(dto.getStartTime() + " 00:00:00");
		}
		if (StringUtils.isNotEmpty(dto.getEndTime())) {
			dto.setEndTime(dto.getEndTime() + " 23:59:59");
		}
		dto.setStatusList(Arrays.asList("1", "2", "3"));
		List<DictVO> dictVOS = dictMapper.getAll();
		Map<String, String> dictMap = dictVOS.stream().filter(dictVO -> dictVO.getDictValue() != null && !dictVO.getDictValue().isEmpty() && dictVO.getDictLabel() != null && !dictVO.getDictLabel().isEmpty()) // 过滤掉任何字段为空的
				.collect(Collectors.toMap(DictVO::getDictValue, DictVO::getDictLabel, (v1, v2) -> v1)); // 保留第一个值

		List<SuccessTaskStaticVo> list = taskMapper.failTaskStatic(dto);
		list.forEach(bean -> bean.setTypeName(dictMap.getOrDefault(bean.getType(), "未知")));
		return list;
	}


	@Override
	public List<FailPadDetailVo> failPadCodeList(TaskStaticDTO dto) {
		if (StringUtils.isNotEmpty(dto.getStartTime())) {
			dto.setStartTime(dto.getStartTime() + " 00:00:00");
		}
		if (StringUtils.isNotEmpty(dto.getEndTime())) {
			dto.setEndTime(dto.getEndTime() + " 23:59:59");
		}
		return taskMapper.failPadCodeList(dto);
	}

	@Override
	public Page<AllTaskVO> allListTasksV2(TaskDTO taskDTO){
		com.github.pagehelper.Page<Object> pageHelper = PageHelper.startPage(taskDTO.getPage(), taskDTO.getRows());
		pageHelper.setCount(false);
		if (!SecurityUtils.isAdmin()) {
			taskDTO.setCustomerId(SecurityUtils.getUserId());
		}
		// 获取所有需要前端展示的任务类型编码
		List<Integer> frontShowTaskTypes = new ArrayList<>();
		for (TaskTypeAndChannelEnum taskType : TaskTypeAndChannelEnum.values()) {
			if (Boolean.TRUE.equals(taskType.getFrontShow())) {
				frontShowTaskTypes.add(taskType.getTaskCode());
			}
		}

		// 如果已有taskTypes条件，则与frontShow任务取交集；否则直接使用frontShow任务
		if (taskDTO.getTaskTypes() != null && !taskDTO.getTaskTypes().isEmpty()) {
			taskDTO.getTaskTypes().retainAll(frontShowTaskTypes);
		} else if (taskDTO.getAllTaskTypes() != null && !taskDTO.getAllTaskTypes().isEmpty()) {
			List<Integer> allowedTypes = new ArrayList<>(taskDTO.getAllTaskTypes());
			allowedTypes.retainAll(frontShowTaskTypes);
			taskDTO.setAllTaskTypes(allowedTypes);
		} else {
			// 如果没有设置任何任务类型过滤条件，则使用所有frontShow任务
			taskDTO.setTaskTypes(frontShowTaskTypes);
		}
		List<AllTaskVO> taskVOS;
		// 用于查询性能提升 不同的参数场景用不同的方式
		// 目前1和3的方式查询使用allListTasks 比较快 其他的查询走allListTasks2比较快
		if (taskDTO.getIdNoType() != null && (taskDTO.getIdNoType() == 1 || taskDTO.getIdNoType() == 3)) {
			taskVOS = taskMapper.allListTasks(taskDTO);
		} else {
			taskVOS = taskMapper.allListTasks2(taskDTO);
		}

		// 批量获取请求参数
		// 批量获取镜像版本
		if (CollUtil.isNotEmpty(taskVOS)) {
			// 主任务id
			List<Long> masterTaskIdList = new ArrayList<>();
			// 板卡编号或者实例编号 用于获取请求参数
			List<String> keyList = new ArrayList<>();
			// 镜像id
			Set<String> imageIds = new HashSet<>();
			// 实例编号
			Set<String> padCodes = new HashSet<>();
			taskVOS.forEach(taskVO -> {
				masterTaskIdList.add(taskVO.getMasterTaskId());
				if (StrUtil.isNotEmpty(taskVO.getImageId())) {
					imageIds.add(taskVO.getImageId());
				}
				if (StrUtil.isNotEmpty(taskVO.getLastImageId())) {
					imageIds.add(taskVO.getLastImageId());
				}
				if (StrUtil.isNotEmpty(taskVO.getInstanceId())) {
					keyList.add(taskVO.getInstanceId());
					padCodes.add(taskVO.getInstanceId());
				} else if (StrUtil.isNotEmpty(taskVO.getDeviceCode())) {
					keyList.add(taskVO.getDeviceCode());
				}

			});
			// 请求参数批量查询
			if (CollUtil.isNotEmpty(keyList)) {
				List<TaskQueue> taskQueueList = taskMapper.batchSelectList(masterTaskIdList, keyList);
				if (CollUtil.isNotEmpty(taskQueueList)) {
					Map<String, String> map = taskQueueList.stream().collect(Collectors.toMap(task -> task.getMasterTaskId() + "_" + task.getKey(), TaskQueue::getContentJson, (key1, key2) -> key1));
					taskVOS.forEach(taskVO -> {
						String mapKey = null;
						if (StrUtil.isNotEmpty(taskVO.getInstanceId())) {
							mapKey = taskVO.getMasterTaskId() + "_" + taskVO.getInstanceId();
						} else if (StrUtil.isNotEmpty(taskVO.getDeviceCode())) {
							mapKey = taskVO.getMasterTaskId() + "_" + taskVO.getDeviceCode();
						}
						taskVO.setContentJson(StrUtil.isNotEmpty(mapKey) ? map.get(mapKey) : null);
					});
				}
			}

			// 镜像版本批量查询
			if (CollUtil.isNotEmpty(imageIds)) {
				List<CustomerUploadImage> customerUploadImages = customerUploadImageMapper.batchSelectList(new ArrayList<>(imageIds));
				if (CollUtil.isNotEmpty(customerUploadImages)) {
					Map<String, String> map = customerUploadImages.stream().collect(Collectors.toMap(CustomerUploadImage::getUniqueId, CustomerUploadImage::getRomVersion, (key1, key2) -> key1));
					taskVOS.forEach(taskVO -> {
						if (StrUtil.isNotEmpty(taskVO.getImageId())) {
							taskVO.setImageIdRomVersion(map.get(taskVO.getImageId()));
						}
						if (StrUtil.isNotEmpty(taskVO.getLastImageId())) {
							taskVO.setLastImageIdRomVersion(map.get(taskVO.getLastImageId()));
						}
					});
				}
			}

			// 实例编号查询所属板卡编号
			if (CollUtil.isNotEmpty(padCodes)) {
				List<PadAndDeviceInfoVO> padAndDeviceInfoVOS = padMapper.selectPadAndDeviceInfos(new ArrayList<>(padCodes));
				if (CollUtil.isNotEmpty(padAndDeviceInfoVOS)) {
					Map<String, String> map = padAndDeviceInfoVOS.stream().collect(Collectors.toMap(PadAndDeviceInfoVO::getPadCode, PadAndDeviceInfoVO::getDeviceCode, (key1, key2) -> key1));
					taskVOS.forEach(taskVO -> {
						if (StrUtil.isNotEmpty(taskVO.getInstanceId())) {
							taskVO.setDeviceCode(map.get(taskVO.getInstanceId()));
						}
					});
				}
			}
		}


		taskVOS.forEach(taskVO -> {
			if (taskVO.getCustomerId() != null) {
				CustomerVO customerVO = customerMapper.selectByPrimaryKey(taskVO.getCustomerId());
				if (customerVO != null) {
					taskVO.setCustomerAccount(customerVO.getCustomerAccount());
					taskVO.setCustomerCode(customerVO.getCustomerCode());
				}
			}
			// getDictLabel(taskVO);
			// taskVO.setTaskName(taskVO.getTaskTypeName());
			TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(taskVO.getTaskType());
			taskVO.setTaskSelectType(taskTypeAndChannelEnum.getTaskSelectType());
			Boolean isPadUploadTask = TaskSelectTypeConstants.PAD_UPLOAD_TASK.getType().equals(taskTypeAndChannelEnum.getTaskSelectType());
			if (taskVO.getCustomerFileId() != null && isPadUploadTask) {
				FileVO fileVO = userFileMapper.selectByFile(taskVO.getCustomerFileId());
				if (fileVO != null) {
					taskVO.setFileId(fileVO.getUniqueId());
					taskVO.setFileName(fileVO.getFileName());
					if (StrUtil.isNotEmpty(fileVO.getFileName())) {
						taskVO.setTaskName("文件上传[" + fileVO.getFileName() + "]");
					}
				}
			}

			if (taskVO.getCustomerFileId() != null && !isPadUploadTask) {
				FileVO fileVO = userFileMapper.selectByApp(taskVO.getCustomerFileId());
				if (fileVO != null) {
					taskVO.setAppId(fileVO.getCustomerAppId());
					taskVO.setAppName(fileVO.getAppName());
					taskVO.setPackageName(fileVO.getPackageName());
					taskVO.setVersion(fileVO.getVersion());
					if (StrUtil.isNotEmpty(fileVO.getAppName())) {
						taskVO.setTaskName(taskVO.getTaskTypeName() + "[" + fileVO.getAppName() + "]");
					}
				}
			} else if (UNINSTALL_APP.getType().equals(taskVO.getTaskType()) && StrUtil.isNotEmpty(taskVO.getTaskContent())) {
				// 应用卸载customerFileId为空 则尝试从taskContent中获取
				PadUninstallAppInfoVO padUninstallAppInfoVO = JSONUtil.toBean(taskVO.getTaskContent(), PadUninstallAppInfoVO.class);
				taskVO.setAppName(padUninstallAppInfoVO.getAppName());
				taskVO.setPackageName(padUninstallAppInfoVO.getPackageName());
				taskVO.setVersion(padUninstallAppInfoVO.getVersionName());
				if (StrUtil.isNotEmpty(padUninstallAppInfoVO.getAppName())) {
					taskVO.setTaskName(taskVO.getTaskTypeName() + "[" + padUninstallAppInfoVO.getAppName() + "]");
				}
			}
			// fill operate app information
			parseOperateAppInfo(taskVO);

		});
		setAllTaskDictLabel(taskVOS);
		return new Page<>(taskVOS);
	}

	@Override
	public Page<AllTaskVO> allListTasks(TaskDTO taskDTO) {
		PageHelper.startPage(taskDTO.getPage(), taskDTO.getRows());
		if (!SecurityUtils.isAdmin()) {
			taskDTO.setCustomerId(SecurityUtils.getUserId());
		}
		// 获取所有需要前端展示的任务类型编码
		List<Integer> frontShowTaskTypes = new ArrayList<>();
		for (TaskTypeAndChannelEnum taskType : TaskTypeAndChannelEnum.values()) {
			if (Boolean.TRUE.equals(taskType.getFrontShow())) {
				frontShowTaskTypes.add(taskType.getTaskCode());
			}
		}

		// 如果已有taskTypes条件，则与frontShow任务取交集；否则直接使用frontShow任务
		if (taskDTO.getTaskTypes() != null && !taskDTO.getTaskTypes().isEmpty()) {
			taskDTO.getTaskTypes().retainAll(frontShowTaskTypes);
		} else if (taskDTO.getAllTaskTypes() != null && !taskDTO.getAllTaskTypes().isEmpty()) {
			List<Integer> allowedTypes = new ArrayList<>(taskDTO.getAllTaskTypes());
			allowedTypes.retainAll(frontShowTaskTypes);
			taskDTO.setAllTaskTypes(allowedTypes);
		} else {
			// 如果没有设置任何任务类型过滤条件，则使用所有frontShow任务
			taskDTO.setTaskTypes(frontShowTaskTypes);
		}
		List<AllTaskVO> taskVOS;
		// 用于查询性能提升 不同的参数场景用不同的方式
		// 目前1和3的方式查询使用allListTasks 比较快 其他的查询走allListTasks2比较快
		if (taskDTO.getIdNoType() != null && (taskDTO.getIdNoType() == 1 || taskDTO.getIdNoType() == 3)) {
			taskVOS = taskMapper.allListTasks(taskDTO);
		} else {
			taskVOS = taskMapper.allListTasks2(taskDTO);
		}

		// 批量获取请求参数
		// 批量获取镜像版本
		if (CollUtil.isNotEmpty(taskVOS)) {
			// 主任务id
			List<Long> masterTaskIdList = new ArrayList<>();
			// 板卡编号或者实例编号 用于获取请求参数
			List<String> keyList = new ArrayList<>();
			// 镜像id
			Set<String> imageIds = new HashSet<>();
			// 实例编号
			Set<String> padCodes = new HashSet<>();
			taskVOS.forEach(taskVO -> {
				masterTaskIdList.add(taskVO.getMasterTaskId());
				if (StrUtil.isNotEmpty(taskVO.getImageId())) {
					imageIds.add(taskVO.getImageId());
				}
				if (StrUtil.isNotEmpty(taskVO.getLastImageId())) {
					imageIds.add(taskVO.getLastImageId());
				}
				if (StrUtil.isNotEmpty(taskVO.getInstanceId())) {
					keyList.add(taskVO.getInstanceId());
					padCodes.add(taskVO.getInstanceId());
				} else if (StrUtil.isNotEmpty(taskVO.getDeviceCode())) {
					keyList.add(taskVO.getDeviceCode());
				}

			});
			// 请求参数批量查询
			if (CollUtil.isNotEmpty(keyList)) {
				List<TaskQueue> taskQueueList = taskMapper.batchSelectList(masterTaskIdList, keyList);
				if (CollUtil.isNotEmpty(taskQueueList)) {
					Map<String, String> map = taskQueueList.stream().collect(Collectors.toMap(task -> task.getMasterTaskId() + "_" + task.getKey(), TaskQueue::getContentJson, (key1, key2) -> key1));
					taskVOS.forEach(taskVO -> {
						String mapKey = null;
						if (StrUtil.isNotEmpty(taskVO.getInstanceId())) {
							mapKey = taskVO.getMasterTaskId() + "_" + taskVO.getInstanceId();
						} else if (StrUtil.isNotEmpty(taskVO.getDeviceCode())) {
							mapKey = taskVO.getMasterTaskId() + "_" + taskVO.getDeviceCode();
						}
						taskVO.setContentJson(StrUtil.isNotEmpty(mapKey) ? map.get(mapKey) : null);
					});
				}
			}

			// 镜像版本批量查询
			if (CollUtil.isNotEmpty(imageIds)) {
				List<CustomerUploadImage> customerUploadImages = customerUploadImageMapper.batchSelectList(new ArrayList<>(imageIds));
				if (CollUtil.isNotEmpty(customerUploadImages)) {
					Map<String, String> map = customerUploadImages.stream().collect(Collectors.toMap(CustomerUploadImage::getUniqueId, CustomerUploadImage::getRomVersion, (key1, key2) -> key1));
					taskVOS.forEach(taskVO -> {
						if (StrUtil.isNotEmpty(taskVO.getImageId())) {
							taskVO.setImageIdRomVersion(map.get(taskVO.getImageId()));
						}
						if (StrUtil.isNotEmpty(taskVO.getLastImageId())) {
							taskVO.setLastImageIdRomVersion(map.get(taskVO.getLastImageId()));
						}
					});
				}
			}

			// 实例编号查询所属板卡编号
			if (CollUtil.isNotEmpty(padCodes)) {
				List<PadAndDeviceInfoVO> padAndDeviceInfoVOS = padMapper.selectPadAndDeviceInfos(new ArrayList<>(padCodes));
				if (CollUtil.isNotEmpty(padAndDeviceInfoVOS)) {
					Map<String, String> map = padAndDeviceInfoVOS.stream().collect(Collectors.toMap(PadAndDeviceInfoVO::getPadCode, PadAndDeviceInfoVO::getDeviceCode, (key1, key2) -> key1));
					taskVOS.forEach(taskVO -> {
						if (StrUtil.isNotEmpty(taskVO.getInstanceId())) {
							taskVO.setDeviceCode(map.get(taskVO.getInstanceId()));
						}
					});
				}
			}
		}


		taskVOS.forEach(taskVO -> {
			if (taskVO.getCustomerId() != null) {
				CustomerVO customerVO = customerMapper.selectByPrimaryKey(taskVO.getCustomerId());
				if (customerVO != null) {
					taskVO.setCustomerAccount(customerVO.getCustomerAccount());
					taskVO.setCustomerCode(customerVO.getCustomerCode());
				}
			}
			// getDictLabel(taskVO);
			// taskVO.setTaskName(taskVO.getTaskTypeName());
			TaskTypeAndChannelEnum taskTypeAndChannelEnum = TaskTypeAndChannelEnum.fromCode(taskVO.getTaskType());
			taskVO.setTaskSelectType(taskTypeAndChannelEnum.getTaskSelectType());
			Boolean isPadUploadTask = TaskSelectTypeConstants.PAD_UPLOAD_TASK.getType().equals(taskTypeAndChannelEnum.getTaskSelectType());
			if (taskVO.getCustomerFileId() != null && isPadUploadTask) {
				FileVO fileVO = userFileMapper.selectByFile(taskVO.getCustomerFileId());
				if (fileVO != null) {
					taskVO.setFileId(fileVO.getUniqueId());
					taskVO.setFileName(fileVO.getFileName());
					if (StrUtil.isNotEmpty(fileVO.getFileName())) {
						taskVO.setTaskName("文件上传[" + fileVO.getFileName() + "]");
					}
				}
			}

			if (taskVO.getCustomerFileId() != null && !isPadUploadTask) {
				FileVO fileVO = userFileMapper.selectByApp(taskVO.getCustomerFileId());
				if (fileVO != null) {
					taskVO.setAppId(fileVO.getCustomerAppId());
					taskVO.setAppName(fileVO.getAppName());
					taskVO.setPackageName(fileVO.getPackageName());
					taskVO.setVersion(fileVO.getVersion());
					if (StrUtil.isNotEmpty(fileVO.getAppName())) {
						taskVO.setTaskName(taskVO.getTaskTypeName() + "[" + fileVO.getAppName() + "]");
					}
				}
			} else if (UNINSTALL_APP.getType().equals(taskVO.getTaskType()) && StrUtil.isNotEmpty(taskVO.getTaskContent())) {
				// 应用卸载customerFileId为空 则尝试从taskContent中获取
				PadUninstallAppInfoVO padUninstallAppInfoVO = JSONUtil.toBean(taskVO.getTaskContent(), PadUninstallAppInfoVO.class);
				taskVO.setAppName(padUninstallAppInfoVO.getAppName());
				taskVO.setPackageName(padUninstallAppInfoVO.getPackageName());
				taskVO.setVersion(padUninstallAppInfoVO.getVersionName());
				if (StrUtil.isNotEmpty(padUninstallAppInfoVO.getAppName())) {
					taskVO.setTaskName(taskVO.getTaskTypeName() + "[" + padUninstallAppInfoVO.getAppName() + "]");
				}
			}
			// fill operate app information
			parseOperateAppInfo(taskVO);

		});
		setAllTaskDictLabel(taskVOS);
		return new Page<>(taskVOS);
	}

	public void setAllTaskDictLabel(List<AllTaskVO> taskVOS) {
		List<DictVO> taskTypeDictVOList = dictMapper.selectByDictTypeList("task_type");
		List<DictVO> executionStateDictVOList = dictMapper.selectByDictTypeList("task_execution_state");
		List<DictVO> taskSourceDictVOList = dictMapper.selectByDictTypeList("task_source");

		Map<String, DictVO> taskTypeMap = ListToMapUtil.map(taskTypeDictVOList, DictVO::getDictValue);
		Map<String, DictVO> executionStateMap = ListToMapUtil.map(executionStateDictVOList, DictVO::getDictValue);
		Map<String, DictVO> taskSourceMap = ListToMapUtil.map(taskSourceDictVOList, DictVO::getDictValue);

		for (AllTaskVO taskVO : taskVOS) {
			if (ObjectUtil.isNotNull(taskVO.getTaskType())) {
				if (taskTypeMap.containsKey(taskVO.getTaskType().toString())) {
					taskVO.setTaskTypeName(taskTypeMap.get(taskVO.getTaskType().toString()).getDictLabel());
					taskVO.setTaskName(taskVO.getTaskTypeName());
				}
			}
			if (ObjectUtil.isNotNull(taskVO.getExecuteStatus())) {
				if (executionStateMap.containsKey(taskVO.getExecuteStatus().toString())) {
					taskVO.setExecuteStatusName(executionStateMap.get(taskVO.getExecuteStatus().toString()).getDictLabel());
				}
			}
			if (StrUtil.isNotBlank(taskVO.getTaskSource())) {
				if (taskSourceMap.containsKey(taskVO.getTaskSource())) {
					taskVO.setTaskSourceName(taskSourceMap.get(taskVO.getTaskSource()).getDictLabel());
				}
			}
		}
	}


	public void setTaskDictLabel(List<TaskVO> taskVOS) {
		List<DictVO> taskTypeDictVOList = dictMapper.selectByDictTypeList("task_type");
		List<DictVO> executionStateDictVOList = dictMapper.selectByDictTypeList("task_execution_state");
		List<DictVO> taskSourceDictVOList = dictMapper.selectByDictTypeList("task_source");

		Map<String, DictVO> taskTypeMap = ListToMapUtil.map(taskTypeDictVOList, DictVO::getDictValue);
		Map<String, DictVO> executionStateMap = ListToMapUtil.map(executionStateDictVOList, DictVO::getDictValue);
		Map<String, DictVO> taskSourceMap = ListToMapUtil.map(taskSourceDictVOList, DictVO::getDictValue);

		for (TaskVO taskVO : taskVOS) {
			if (ObjectUtil.isNotNull(taskVO.getTaskType())) {
				if (taskTypeMap.containsKey(taskVO.getTaskType().toString())) {
					taskVO.setTaskTypeName(taskTypeMap.get(taskVO.getTaskType().toString()).getDictLabel());
					taskVO.setTaskName(taskVO.getTaskTypeName());
				}
			}
			if (ObjectUtil.isNotNull(taskVO.getExecuteStatus())) {
				if (executionStateMap.containsKey(taskVO.getExecuteStatus().toString())) {
					taskVO.setExecuteStatusName(executionStateMap.get(taskVO.getExecuteStatus().toString()).getDictLabel());
				}
			}
			if (StrUtil.isNotBlank(taskVO.getTaskSource())) {
				if (taskSourceMap.containsKey(taskVO.getTaskSource())) {
					taskVO.setTaskSourceName(taskSourceMap.get(taskVO.getTaskSource()).getDictLabel());
				}
			}
		}
	}


	public void getDictLabel(TaskVO taskVO) {
		if (ObjectUtil.isNotNull(taskVO.getTaskType())) {
			DictVO taskType = dictMapper.selectByDictTypeAndDictValue("task_type", taskVO.getTaskType());
			if (ObjectUtil.isNotNull(taskType)) {
				taskVO.setTaskTypeName(taskType.getDictLabel());
				if (StringUtils.isBlank(taskVO.getTaskName())) {
					taskVO.setTaskName(taskVO.getTaskTypeName());
				}
			}
		}
		if (ObjectUtil.isNotNull(taskVO.getExecuteStatus())) {
			DictVO taskExecutionState = dictMapper.selectByDictTypeAndDictValue("task_execution_state", taskVO.getExecuteStatus());
			if (ObjectUtil.isNotNull(taskExecutionState)) {
				taskVO.setExecuteStatusName(taskExecutionState.getDictLabel());
			}
		}
		if (StrUtil.isNotBlank(taskVO.getTaskSource())) {
			DictVO taskSource = dictMapper.selectByDictTypeAndDictValueStr("task_source", taskVO.getTaskSource());
			if (ObjectUtil.isNotNull(taskSource)) {
				taskVO.setTaskSourceName(taskSource.getDictLabel());
			}
		}
	}

	public void getDictLabel(AllTaskVO taskVO) {
		if (ObjectUtil.isNotNull(taskVO.getTaskType())) {
			DictVO taskType = dictMapper.selectByDictTypeAndDictValue("task_type", taskVO.getTaskType());
			if (ObjectUtil.isNotNull(taskType)) {
				taskVO.setTaskTypeName(taskType.getDictLabel());
				if (StringUtils.isBlank(taskVO.getTaskName())) {
					taskVO.setTaskName(taskVO.getTaskTypeName());
				}
			}
		}
		if (ObjectUtil.isNotNull(taskVO.getExecuteStatus())) {
			DictVO taskExecutionState = dictMapper.selectByDictTypeAndDictValue("task_execution_state", taskVO.getExecuteStatus());
			if (ObjectUtil.isNotNull(taskExecutionState)) {
				taskVO.setExecuteStatusName(taskExecutionState.getDictLabel());
			}
		}
		if (StrUtil.isNotBlank(taskVO.getTaskSource())) {
			DictVO taskSource = dictMapper.selectByDictTypeAndDictValueStr("task_source", taskVO.getTaskSource());
			if (ObjectUtil.isNotNull(taskSource)) {
				taskVO.setTaskSourceName(taskSource.getDictLabel());
			}
		}
	}

	public void getBackupDictLabel(TaskBackupVO taskBackupVO) {
		if (ObjectUtil.isNotNull(taskBackupVO.getBackupType())) {
			DictVO taskType = dictMapper.selectByDictTypeAndDictValue("backup_type", taskBackupVO.getBackupType());
			if (ObjectUtil.isNotNull(taskType)) {
				taskBackupVO.setBackupTypeName(taskType.getDictLabel());
			}
		}
		if (ObjectUtil.isNotNull(taskBackupVO.getStatus())) {
			DictVO taskExecutionState = dictMapper.selectByDictTypeAndDictValue("task_execution_state", taskBackupVO.getStatus());
			if (ObjectUtil.isNotNull(taskExecutionState)) {
				taskBackupVO.setStatusName(taskExecutionState.getDictLabel());
			}
		}
		if (StrUtil.isNotBlank(taskBackupVO.getTaskSource())) {
			DictVO taskSource = dictMapper.selectByDictTypeAndDictValueStr("task_source", taskBackupVO.getTaskSource());
			if (ObjectUtil.isNotNull(taskSource)) {
				taskBackupVO.setTaskSource(taskSource.getDictLabel());
			}
		}
	}

	public void getRestoreDictLabel(TaskRestoreVO taskRestoreVO) {
		if (ObjectUtil.isNotNull(taskRestoreVO.getStatus())) {
			DictVO taskExecutionState = dictMapper.selectByDictTypeAndDictValue("task_execution_state", taskRestoreVO.getStatus());
			if (ObjectUtil.isNotNull(taskExecutionState)) {
				taskRestoreVO.setStatusName(taskExecutionState.getDictLabel());
			}
		}
		if (StrUtil.isNotBlank(taskRestoreVO.getTaskSource())) {
			DictVO taskSource = dictMapper.selectByDictTypeAndDictValueStr("task_source", taskRestoreVO.getTaskSource());
			if (ObjectUtil.isNotNull(taskSource)) {
				taskRestoreVO.setTaskSource(taskSource.getDictLabel());
			}
		}
	}
}
