package net.armcloud.paas.manage.controller;

import com.alibaba.fastjson2.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.armcloud.paas.manage.client.internal.dto.CephClusterChartDTO;
import net.armcloud.paas.manage.client.internal.dto.CephPressureQueryDTO;
import net.armcloud.paas.manage.client.internal.feign.CephMetricDataClient;
import net.armcloud.paas.manage.domain.Result;
import net.armcloud.paas.manage.model.dto.TrafficSummaryDTO;
import net.armcloud.paas.manage.model.vo.TrafficSummaryVO;
import net.armcloud.paas.manage.service.ICusTrafficInfoService;
import net.armcloud.paas.manage.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static org.springframework.util.ObjectUtils.isEmpty;

@Slf4j
@RestController
@RequestMapping("/manage/trafficStatist")
@Api(tags = "流量统计")
public class TrafficStatisticsController {

    @Autowired
    private ICusTrafficInfoService cusTrafficInfoService;

    // @Autowired
    // private IPadSystemConfigDataService padSystemConfigDataService;
    //
    // @Autowired
    // private IDeviceSystemConfigDataService deviceSystemConfigDataService;

    @Autowired
    private CephMetricDataClient cephMetricDataClient;

    @RequestMapping(value = "/summary", method = RequestMethod.POST)
    @ApiOperation(value = "汇总统计", httpMethod = "POST", notes = "汇总统计")
    public Result<TrafficSummaryVO> summary(@RequestBody TrafficSummaryDTO param) {
        if (!SecurityUtils.isAdmin()) {
            param.setCustomerId(SecurityUtils.getUserId());
        }
        log.info("汇总统计请求入参:{}", JSON.toJSONString(param));
        return Result.ok(cusTrafficInfoService.summaryList(param));
    }

    @RequestMapping(value = "/padBandwidth", method = RequestMethod.POST)
    @ApiOperation(value = "实例带宽统计", httpMethod = "POST", notes = "实例带宽统计")
    public Result<TrafficSummaryVO> padBandwidth(@RequestBody TrafficSummaryDTO param) {
        if (isEmpty(param.getPadCode())) {
            return Result.fail("实例编码不能为空");
        }
        log.info("实例带宽统计用户：{}",param.getCustomerId());
        return Result.ok(cusTrafficInfoService.padBandwidth(param));
    }

    @RequestMapping(value = "/deviceBandwidth", method = RequestMethod.POST)
    @ApiOperation(value = "板卡带宽统计", httpMethod = "POST", notes = "板卡带宽统计")
    public Result<TrafficSummaryVO> deviceBandwidth(@RequestBody TrafficSummaryDTO param) {
        if (isEmpty(param.getDeviceCode())) {
            return Result.fail("板卡编号不能为空");
        }
        return Result.ok(cusTrafficInfoService.deviceBandwidth(param));
    }

    // @RequestMapping(value = "/padSystemData", method = RequestMethod.POST)
    // @ApiOperation(value = "实例系统数据埋点", httpMethod = "POST", notes = "实例系统数据埋点")
    // public Result<List<SystemDataVO>> padSystemData(@RequestBody SystemDataSummeryDTO param) {
    //     if (isEmpty(param.getPadCode())) {
    //         return Result.fail("实例编码不能为空");
    //     }
    //     List<SystemDataVO> List = padSystemConfigDataService.padSystemDataList(param);
    //     return Result.ok(List);
    // }

    // @RequestMapping(value = "/deviceSystemData", method = RequestMethod.POST)
    // @ApiOperation(value = "板卡系统数据埋点", httpMethod = "POST", notes = "板卡系统数据埋点")
    // public Result<List<SystemDataVO>> deviceSystemData(@RequestBody SystemDataSummeryDTO param) {
    //     if (isEmpty(param.getDeviceCode())) {
    //         return Result.fail("板卡编号不能为空");
    //     }
    //     List<SystemDataVO> List = deviceSystemConfigDataService.deviceSystemDataList(param);
    //     return Result.ok(List);
    // }


    @PostMapping("/traffic/edge/ceph/chart")
    @ApiOperation(value = "查询Ceph压力折线图数据（按集群聚合）")
    public Result<List<CephClusterChartDTO>> getCephPressureChart(@RequestBody CephPressureQueryDTO queryDTO) {
        return cephMetricDataClient.getCephPressureChart(queryDTO);
    }
}
