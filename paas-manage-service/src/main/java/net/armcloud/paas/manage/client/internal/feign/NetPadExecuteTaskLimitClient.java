package net.armcloud.paas.manage.client.internal.feign;

import net.armcloud.paas.manage.client.internal.dto.NetPadTaskExecuteLimitExecutedDTO;
import net.armcloud.paas.manage.client.internal.feign.fallback.NetPadExecuteTaskLimitFallbackFactory;
import net.armcloud.paas.manage.domain.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @description paas-center-core服务任务执行完成后处理限流
 * @date 2025/07/29
 */
@FeignClient(
        name = "paas-center-core",
        contextId = "paas-center-core-task-execute-limit",
        fallbackFactory = NetPadExecuteTaskLimitFallbackFactory.class
)
public interface NetPadExecuteTaskLimitClient {

    @PostMapping("/netpadv2/limit/executed")
    Result<Void> executed(@RequestBody NetPadTaskExecuteLimitExecutedDTO dto);

}
